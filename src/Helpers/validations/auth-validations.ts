import * as yup from 'yup';

// Email validation schema
export const emailValidation = yup
  .string()
  .required('Email is required')
  .email('Please enter a valid email address')
  .matches(/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/, 'Email format is invalid')
  .max(50, 'Email must be less than 50 characters');

// Password validation schema
export const passwordValidation = yup
  .string()
  .required('Password is required')
  .min(8, 'Password must be at least 8 characters')
  .max(128, 'Password must be less than 128 characters')
  .matches(/^(?=.*[a-z])/, 'Password must contain at least one lowercase letter')
  // .matches(/^(?=.*[A-Z])/, 'Password must contain at least one uppercase letter')
  .matches(/^(?=.*\d)/, 'Password must contain at least one number')
  .matches(/^(?=.*[@$!%*?&])/, 'Password must contain at least one special character (@$!%*?&)');

// Login form validation schema
export const loginValidationSchema = yup.object({
  username: emailValidation.label('Email'),
  password: passwordValidation.label('Password'),
});
