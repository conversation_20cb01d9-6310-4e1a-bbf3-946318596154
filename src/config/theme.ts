// src/config/theme.ts
import {createTheme, ThemeOptions} from '@mui/material/styles';
import {colors} from 'Providers/theme/colors';

const themeOptions: ThemeOptions = {
  palette: {
    mode: 'light',
    primary: {
      main: colors.black,
    },
    background: {
      default: colors.white100,
      paper: colors.white,
    },
    text: {
      primary: colors.black,
      secondary: colors.darkGrey,
      disabled: colors.lightGrey,
    },
  },
  typography: {
    fontFamily: "'Lato', 'Inter', 'Roboto', sans-serif",
    h1: {
      fontSize: '2.25rem',
      fontWeight: 700,
    },
    h6: {
      fontSize: '1.25rem',
      fontWeight: 500,
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
    },
    button: {
      textTransform: 'none',
      fontSize: '0.875rem',
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          padding: '0.75rem 1.5rem',
          borderRadius: 8,
        },
        containedPrimary: {
          color: colors.white,
          backgroundColor: colors.black,
          '&:hover': {
            backgroundColor: colors.darkestGrey,
          },
        },
      },
    },
  },
};

const theme = createTheme(themeOptions);

export default theme;
