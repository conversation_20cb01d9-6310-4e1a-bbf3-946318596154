import {FormControl, FormHelperText, SxProps, Theme} from '@mui/material';
import {DatePicker as MuiDatePicker, DatePickerProps as MuiDatePickerProps} from '@mui/x-date-pickers/DatePicker';
import InputLabel from 'Components/InputLabel';
import React, {memo, useCallback} from 'react';

export interface DatePickerProps {
  id: string;
  label?: string;
  onChange?: (val: Date | null) => void;
  disabled?: boolean;
  errorMessage?: string;
  helperText?: string;
  minDateTime?: Date;
  sx?: SxProps<Theme>;
}

interface Props extends DatePickerProps {
  value?: Date | null;
}

const DatePicker: React.FC<Props & Omit<MuiDatePickerProps, 'renderInput'>> = ({
  id,
  label,
  value,
  onChange,
  errorMessage,
  sx,
  disabled,
  helperText,
  ...rest
}) => {
  const isError = !!errorMessage;
  const handleChange = useCallback(
    (date: Date | null) => {
      if (onChange) onChange(date);
    },
    [onChange],
  );

  return (
    <FormControl sx={{width: 1, ...sx}} data-testid="datePickerFormControl" disabled={disabled} error={isError}>
      {label && <InputLabel htmlFor={id}>{label}</InputLabel>}
      <MuiDatePicker
        slotProps={{
          textField: {
            sx: {marginTop: 2, p: 1},
            id: id,
          },
          inputAdornment: {position: 'start'},
        }}
        disabled={disabled}
        value={value}
        onChange={handleChange}
        {...rest}
      />
      {(isError || helperText) && <FormHelperText>{isError ? errorMessage : helperText}</FormHelperText>}
    </FormControl>
  );
};

export default memo(DatePicker);
