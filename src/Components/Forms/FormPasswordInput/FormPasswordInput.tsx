import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import EyeIcon from 'Assets/EyeIcon';
import EyeOffIcon from 'Assets/EyeOffIcon';
import FormInput from 'Components/Forms/FormInput';
import {InputProps} from 'Components/Input/Input';
import {colors} from 'Providers/theme/colors';
import React, {useCallback, useState} from 'react';

const FormPasswordInput: React.FC<InputProps> = props => {
  const [showPassword, setShowPassword] = useState(false);
  const handleOnClickEvent = useCallback(() => {
    setShowPassword(!showPassword);
  }, [showPassword]);

  return (
    <FormInput
      {...props}
      type={showPassword ? 'text' : 'password'}
      endAdornment={
        <InputAdornment position="end">
          <IconButton aria-label="toggle password visibility" onClick={handleOnClickEvent} edge="end">
            {showPassword ? (
              <EyeIcon style={{color: colors.dark500}} />
            ) : (
              <EyeOffIcon style={{color: colors.dark500}} />
            )}
          </IconButton>
        </InputAdornment>
      }
    />
  );
};

export default FormPasswordInput;
