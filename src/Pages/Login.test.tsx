import {fireEvent, screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import * as useAuth from 'Hooks/useAuth';
import {MemoryRouter} from 'react-router-dom';
import {renderWithStore} from 'Tests/utils/renderWithStore';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import Login from './Login';

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({state: {from: {pathname: '/dashboard'}}}),
  };
});

describe('Login Component', () => {
  const mockLogin = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockNavigate.mockClear();

    // Mock useAuth hook
    vi.spyOn(useAuth, 'default').mockReturnValue({
      login: mockLogin,
      loginLoading: false,
      isLoggedIn: false,
      authData: {
        accessToken: null,
        refreshToken: null,
        expires: null,
        isLoggedIn: false,
      },
      logout: vi.fn(),
      logoutLoading: false,
    });
  });

  const renderLogin = () => {
    return renderWithStore(
      <MemoryRouter>
        <Login />
      </MemoryRouter>,
    );
  };

  it('renders the form with all required fields', () => {
    renderLogin();

    // Check for input fields by their IDs
    expect(screen.getByRole('textbox')).toBeInTheDocument(); // username input
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument(); // password input
    expect(screen.getByRole('button', {name: /Sign In/i})).toBeInTheDocument();
    expect(screen.getByText('Sign in to your account')).toBeInTheDocument();
  });

  it('shows validation errors when form fields are touched and left empty', async () => {
    renderLogin();

    // Get inputs by their IDs
    const usernameInput = screen.getByRole('textbox');
    const passwordInput = screen.getByLabelText(/password/i);

    // Touch and leave empty
    await userEvent.click(usernameInput);
    await userEvent.tab();
    await userEvent.click(passwordInput);
    await userEvent.tab();

    await waitFor(() => {
      expect(screen.getByText('Email is required')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByText('Password is required')).toBeInTheDocument();
    });
  });

  it('displays submit button', () => {
    renderLogin();

    const submitButton = screen.getByRole('button', {name: /Sign In/i});
    expect(submitButton).toBeInTheDocument();
    expect(submitButton).toBeEnabled();
  });

  it('shows loading state when login is in progress', () => {
    // Mock loading state
    vi.spyOn(useAuth, 'default').mockReturnValue({
      login: mockLogin,
      loginLoading: true, // Set loading to true
      isLoggedIn: false,
      authData: {
        accessToken: null,
        refreshToken: null,
        expires: null,
        isLoggedIn: false,
      },
      logout: vi.fn(),
      logoutLoading: false,
    });

    renderLogin();

    const submitButton = screen.getByRole('button', {name: /Signing in.../i});
    expect(submitButton).toBeInTheDocument();
  });

  it('allows typing in form fields', async () => {
    renderLogin();

    const usernameInput = screen.getByRole('textbox');
    const passwordInput = screen.getByPlaceholderText('Enter your password');

    // Fill form with data
    await userEvent.type(usernameInput, '<EMAIL>');
    await userEvent.type(passwordInput, 'password123!');

    // Verify values are set
    expect(usernameInput).toHaveValue('<EMAIL>');
    expect(passwordInput).toHaveValue('password123!');
  });

  it('renders form inputs with correct types', () => {
    renderLogin();

    const usernameInput = screen.getByRole('textbox');
    const passwordInput = screen.getByPlaceholderText('Enter your password');

    expect(usernameInput).toHaveAttribute('type', 'text');
    expect(passwordInput).toHaveAttribute('type', 'password');
  });

  it('validates email format', async () => {
    renderLogin();

    const usernameInput = screen.getByRole('textbox');

    // Enter invalid email
    await userEvent.type(usernameInput, 'invalid-email');
    await userEvent.tab();

    await waitFor(() => {
      expect(screen.getByText(/Please enter a valid email address/i)).toBeInTheDocument();
    });
  });

  it('validates password requirements', async () => {
    renderLogin();

    const passwordInput = screen.getByPlaceholderText('Enter your password');

    // Enter password that doesn't meet requirements
    await userEvent.type(passwordInput, 'weak');
    await userEvent.tab();

    await waitFor(() => {
      expect(screen.getByText(/Password must be at least 8 characters/i)).toBeInTheDocument();
    });
  });

  it('validates password special character requirement', async () => {
    renderLogin();

    const passwordInput = screen.getByPlaceholderText('Enter your password');

    // Enter password without special character
    await userEvent.type(passwordInput, 'password123');
    await userEvent.tab();

    await waitFor(() => {
      expect(screen.getByText(/Password must contain at least one special character/i)).toBeInTheDocument();
    });
  });

  it('has proper form structure', () => {
    renderLogin();
    // Check form exists by finding the form element
    const form = document.querySelector('form');
    expect(form).toBeInTheDocument();

    // Check required form elements exist
    expect(screen.getByRole('textbox')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your password')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: /Sign In/i})).toBeInTheDocument();
  });

  it('renders forgot password link', () => {
    renderLogin();

    const forgotPasswordLink = screen.getByText('Forgot password?');
    expect(forgotPasswordLink).toBeInTheDocument();
    expect(forgotPasswordLink).toHaveAttribute('href', '#');
  });

  it('renders Distek logo', () => {
    renderLogin();

    const logo = screen.getByAltText('DISTEK Logo');
    expect(logo).toBeInTheDocument();
  });

  it('renders welcome text', () => {
    renderLogin();

    expect(screen.getByText('Sign in to your account')).toBeInTheDocument();
    expect(screen.getByText('Please provide your credentials to continue')).toBeInTheDocument();
  });

  it('renders input placeholders correctly', () => {
    renderLogin();

    expect(screen.getByPlaceholderText('Enter your email address')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your password')).toBeInTheDocument();
  });

  it('toggles password visibility when eye icon is clicked', async () => {
    renderLogin();

    const passwordInput = screen.getByPlaceholderText('Enter your password');
    const toggleButton = screen.getByLabelText('toggle password visibility');

    // Initially password should be hidden
    expect(passwordInput).toHaveAttribute('type', 'password');

    // Click to show password
    await userEvent.click(toggleButton);
    expect(passwordInput).toHaveAttribute('type', 'text');

    // Click to hide password again
    await userEvent.click(toggleButton);
    expect(passwordInput).toHaveAttribute('type', 'password');
  });

  it('displays correct field labels', () => {
    renderLogin();

    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('Password')).toBeInTheDocument();
  });

  it('has correct form structure and accessibility', () => {
    renderLogin();

    // Check that form has proper structure
    const form = document.querySelector('form');
    expect(form).toBeInTheDocument();

    // Check that inputs have proper IDs
    const emailInput = screen.getByPlaceholderText('Enter your email address');
    const passwordInput = screen.getByPlaceholderText('Enter your password');

    expect(emailInput).toHaveAttribute('id', 'username');
    expect(passwordInput).toHaveAttribute('id', 'password');
  });

  it('has proper input attributes', () => {
    renderLogin();

    const usernameInput = screen.getByRole('textbox');
    const passwordInput = screen.getByPlaceholderText('Enter your password');

    // Check input attributes
    expect(usernameInput).toHaveAttribute('placeholder', 'Enter your email address');
    expect(passwordInput).toHaveAttribute('placeholder', 'Enter your password');
    expect(usernameInput).toHaveAttribute('type', 'text');
    expect(passwordInput).toHaveAttribute('type', 'password');
  });

  it('renders all visual elements correctly', () => {
    renderLogin();

    // Check all main visual elements are present
    expect(screen.getByText('Sign in to your account')).toBeInTheDocument();
    expect(screen.getByText('Please provide your credentials to continue')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('Password')).toBeInTheDocument();
    expect(screen.getByText('Forgot password?')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: /Sign In/i})).toBeInTheDocument();
  });

  // Tests for form submission functionality
  it('calls login function when form is submitted successfully', async () => {
    mockLogin.mockResolvedValue({success: true, message: null});
    renderLogin();

    const usernameInput = screen.getByRole('textbox');
    const passwordInput = screen.getByPlaceholderText('Enter your password');

    // Fill form with valid data
    await userEvent.type(usernameInput, '<EMAIL>');
    await userEvent.type(passwordInput, 'password123!');

    // Get the form and submit it directly
    const form = document.querySelector('form');
    expect(form).toBeInTheDocument();

    // Trigger form submission by dispatching submit event
    fireEvent.submit(form!);

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        username: '<EMAIL>',
        password: 'password123!',
      });
    });

    // Verify login was called successfully
    expect(mockLogin).toHaveBeenCalledWith({
      username: '<EMAIL>',
      password: 'password123!',
    });
  });

  it('displays error message when login fails', async () => {
    mockLogin.mockResolvedValue({success: false, message: 'Login failed. Please try again.'});
    renderLogin();

    const usernameInput = screen.getByRole('textbox');
    const passwordInput = screen.getByPlaceholderText('Enter your password');

    // Fill form with valid data
    await userEvent.type(usernameInput, '<EMAIL>');
    await userEvent.type(passwordInput, 'password123!');

    // Submit form
    const form = document.querySelector('form');
    fireEvent.submit(form!);

    // Wait for error message to appear
    await waitFor(() => {
      expect(screen.getByText('Login failed. Please try again.')).toBeInTheDocument();
    });

    // Verify error icon is displayed
    const errorIcon = screen.getByTestId('ReportProblemOutlinedIcon');
    expect(errorIcon).toBeInTheDocument();
  });

  it('handles special error message for Invalid Credentials', async () => {
    mockLogin.mockResolvedValue({success: false, message: 'Invalid Credentials'});
    renderLogin();

    const usernameInput = screen.getByRole('textbox');
    const passwordInput = screen.getByPlaceholderText('Enter your password');

    await userEvent.type(usernameInput, '<EMAIL>');
    await userEvent.type(passwordInput, 'password123!');

    const form = document.querySelector('form');
    fireEvent.submit(form!);

    await waitFor(() => {
      expect(screen.getByText('Invalid credentials. Please try again.')).toBeInTheDocument();
    });
  });

  it('clears error message when new login attempt is made', async () => {
    // First, show an error
    mockLogin.mockResolvedValueOnce({success: false, message: 'Login failed'});
    renderLogin();

    const usernameInput = screen.getByRole('textbox');
    const passwordInput = screen.getByPlaceholderText('Enter your password');

    await userEvent.type(usernameInput, '<EMAIL>');
    await userEvent.type(passwordInput, 'password123!');

    const form = document.querySelector('form');
    fireEvent.submit(form!);

    // Wait for error to appear
    await waitFor(() => {
      expect(screen.getByText('Login failed')).toBeInTheDocument();
    });

    // Now mock successful login and submit again
    mockLogin.mockResolvedValueOnce({success: true, message: null});
    fireEvent.submit(form!);

    // Error should be cleared (setLoginError(null) is called)
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledTimes(2);
    });
  });

  it('navigates to custom location from state', async () => {
    mockLogin.mockResolvedValue({success: true, message: null});

    // Mock location with custom from path
    const mockLocation = {
      state: {from: {pathname: '/dashboard'}},
    };

    // Re-mock the router with custom location
    vi.doMock('react-router-dom', async () => {
      const actual = await vi.importActual('react-router-dom');
      return {
        ...actual,
        useNavigate: () => mockNavigate,
        useLocation: () => mockLocation,
      };
    });

    renderLogin();

    const usernameInput = screen.getByRole('textbox');
    const passwordInput = screen.getByPlaceholderText('Enter your password');

    await userEvent.type(usernameInput, '<EMAIL>');
    await userEvent.type(passwordInput, 'password123!');

    const form = document.querySelector('form');
    fireEvent.submit(form!);

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        username: '<EMAIL>',
        password: 'password123!',
      });
    });
  });

  it('sets submitting state during form submission', async () => {
    // Mock a delayed login response
    mockLogin.mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve({success: true, message: null}), 100)),
    );

    renderLogin();

    const usernameInput = screen.getByRole('textbox');
    const passwordInput = screen.getByPlaceholderText('Enter your password');

    await userEvent.type(usernameInput, '<EMAIL>');
    await userEvent.type(passwordInput, 'password123!');

    const form = document.querySelector('form');
    fireEvent.submit(form!);

    // During submission, the login function should be called
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalled();
    });
  });
});
